<template>
  <div class="production-table">
    <div class="table-container">
      <table class="data-table">
        <thead>
          <tr>
            <th>厂区</th>
            <th>车间</th>
            <th>方位</th>
            <th>放养规格</th>
            <th>日龄</th>
            <th>头/斤</th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="(item, index) in currentPageData"
            :key="index"
            class="table-row"
          >
            <td>{{ item.factory }}</td>
            <td>{{ item.workshop }}</td>
            <td>{{ item.direction }}</td>
            <td>{{ item.specification }}</td>
            <td>{{ item.dayAge }}</td>
            <td>{{ item.countPerJin }}</td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="pagination-info">
      第 {{ currentPage + 1 }} 页 / 共 {{ totalPages }} 页
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from "vue";

interface ProductionItem {
  factory: string;
  workshop: string;
  direction: string;
  specification: string;
  dayAge: number;
  countPerJin: number;
}

interface Props {
  buildingId: number;
}

const props = defineProps<Props>();

const currentPage = ref(0);
const pageSize = 8; // 每页显示8条数据
let pageTimer: ReturnType<typeof setInterval>;

// 根据楼栋ID生成生产数据
const generateProductionData = (buildingId: number): ProductionItem[] => {
  const data: ProductionItem[] = [];
  const workshops = ["1", "2", "3", "4", "5", "6"];
  const directions = ["A区", "B区", "C区", "D区"];
  const specifications = ["2到3公分", "3到4公分", "4到5公分", "5到6公分"];

  // 为每个楼栋生成20-30条数据
  const itemCount = 20 + Math.floor(Math.random() * 10);

  for (let i = 0; i < itemCount; i++) {
    data.push({
      factory: `${buildingId}#楼`,
      workshop: workshops[Math.floor(Math.random() * workshops.length)],
      direction: directions[Math.floor(Math.random() * directions.length)],
      specification:
        specifications[Math.floor(Math.random() * specifications.length)],
      dayAge: 30 + Math.floor(Math.random() * 60), // 30-90天
      countPerJin: 80 + Math.floor(Math.random() * 120), // 80-200头/斤
    });
  }

  return data;
};

const productionData = ref<ProductionItem[]>([]);

// 当前页数据
const currentPageData = computed(() => {
  const start = currentPage.value * pageSize;
  const end = start + pageSize;
  return productionData.value.slice(start, end);
});

// 总页数
const totalPages = computed(() => {
  return Math.ceil(productionData.value.length / pageSize);
});

// 自动翻页
const startAutoPage = () => {
  if (pageTimer) {
    clearInterval(pageTimer);
  }

  pageTimer = setInterval(() => {
    if (totalPages.value > 1) {
      currentPage.value = (currentPage.value + 1) % totalPages.value;
    }
  }, 3000); // 每3秒翻页
};

// 监听楼栋变化
watch(
  () => props.buildingId,
  (newBuildingId) => {
    productionData.value = generateProductionData(newBuildingId);
    currentPage.value = 0;
    startAutoPage();
  },
  { immediate: true }
);

onMounted(() => {
  startAutoPage();
});

onUnmounted(() => {
  if (pageTimer) {
    clearInterval(pageTimer);
  }
});
</script>

<style scoped lang="scss">
.production-table {
  height: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;
}

.table-container {
  flex: 1;
  overflow: hidden;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 12px;

  th {
    background: rgba(14, 252, 255, 0.2);
    color: #0efcff;
    padding: 8px 4px;
    text-align: center;
    border: 1px solid #0efcff;
    font-weight: bold;
    font-size: 11px;
  }

  td {
    padding: 6px 4px;
    text-align: center;
    border: 1px solid #333;
    color: #fff;
    background: rgba(0, 0, 0, 0.3);
  }

  .table-row {
    transition: background-color 0.3s ease;

    &:hover {
      background: rgba(14, 252, 255, 0.1);
    }

    &:nth-child(even) {
      background: rgba(0, 0, 0, 0.2);

      &:hover {
        background: rgba(14, 252, 255, 0.1);
      }
    }
  }
}

.pagination-info {
  text-align: center;
  margin-top: 10px;
  font-size: 12px;
  color: #ccc;
}
</style>
