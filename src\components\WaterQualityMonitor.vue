<template>
  <div class="water-quality-monitor">
    <div class="section-title">水质监测</div>

    <div class="charts-container">
      <div
        v-for="(chart, index) in chartConfigs"
        :key="chart.name"
        class="chart-item"
      >
        <div class="chart-title">{{ chart.name }}</div>
        <div class="chart">
          <EChartsComponent :option="chartOptions[index]" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import EChartsComponent from "./EChartsComponent.vue";
import type { EChartsOption } from "echarts";

interface Props {
  buildingId: number;
}

const props = defineProps<Props>();

const chartConfigs = [
  { name: "水温(°C)", key: "temperature", color: "#ff6b6b", unit: "°C" },
  { name: "PH值", key: "ph", color: "#4ecdc4", unit: "" },
  { name: "总盐(‰)", key: "salinity", color: "#45b7d1", unit: "‰" },
  { name: "溶解氧(mg/L)", key: "oxygen", color: "#96ceb4", unit: "mg/L" },
  { name: "亚硝酸盐(mg/L)", key: "nitrite", color: "#feca57", unit: "mg/L" },
];

// 生成模拟数据
const generateWaterQualityData = (buildingId: number) => {
  const now = new Date();
  const data: Record<string, Array<{ time: string; value: number }>> = {};

  chartConfigs.forEach((config) => {
    data[config.key] = [];

    for (let i = 23; i >= 0; i--) {
      const time = new Date(now.getTime() - i * 60 * 60 * 1000);
      let value = 0;

      // 根据楼栋ID和参数类型生成不同的基础值
      const baseOffset = (buildingId - 1) * 0.1;

      switch (config.key) {
        case "temperature":
          value = 26 + Math.sin(i * 0.3) * 2 + Math.random() * 1 + baseOffset;
          break;
        case "ph":
          value =
            7.5 +
            Math.sin(i * 0.2) * 0.3 +
            Math.random() * 0.2 +
            baseOffset * 0.1;
          break;
        case "salinity":
          value = 15 + Math.sin(i * 0.4) * 1 + Math.random() * 0.5 + baseOffset;
          break;
        case "oxygen":
          value =
            6 +
            Math.sin(i * 0.5) * 0.8 +
            Math.random() * 0.3 +
            baseOffset * 0.5;
          break;
        case "nitrite":
          value =
            0.1 +
            Math.sin(i * 0.6) * 0.05 +
            Math.random() * 0.02 +
            baseOffset * 0.01;
          break;
      }

      data[config.key].push({
        time: time.toLocaleTimeString("zh-CN", {
          hour: "2-digit",
          minute: "2-digit",
        }),
        value: Number(value.toFixed(2)),
      });
    }
  });

  return data;
};

// 生成图表选项的计算属性
const chartOptions = computed(() => {
  const data = generateWaterQualityData(props.buildingId);
  return chartConfigs.map((config) =>
    createChartOption(config, data[config.key])
  );
});

// 创建图表配置
const createChartOption = (
  config: (typeof chartConfigs)[0],
  data: Array<{ time: string; value: number }>
) => {
  return {
    grid: {
      left: "10%",
      right: "5%",
      top: "15%",
      bottom: "20%",
    },
    xAxis: {
      type: "category",
      data: data.map((item) => item.time),
      axisLine: {
        lineStyle: { color: "#333" },
      },
      axisLabel: {
        color: "#ccc",
        fontSize: 10,
        interval: 3,
      },
    },
    yAxis: {
      type: "value",
      axisLine: {
        lineStyle: { color: "#333" },
      },
      axisLabel: {
        color: "#ccc",
        fontSize: 10,
        formatter: `{value}${config.unit}`,
      },
      splitLine: {
        lineStyle: { color: "#333", type: "dashed" },
      },
    },
    series: [
      {
        data: data.map((item) => item.value),
        type: "line",
        smooth: true,
        lineStyle: {
          color: config.color,
          width: 2,
        },
        itemStyle: {
          color: config.color,
        },
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: config.color + "40" },
              { offset: 1, color: config.color + "10" },
            ],
          },
        },
        symbol: "circle",
        symbolSize: 4,
      },
    ],
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(0, 0, 0, 0.8)",
      borderColor: config.color,
      textStyle: { color: "#fff" },
      formatter: (params: any) => {
        const point = params[0];
        return `${point.name}<br/>${config.name}: ${point.value}${config.unit}`;
      },
    },
  };
};

// 监听楼栋变化，重新生成图表选项
watch(
  () => props.buildingId,
  () => {
    // 计算属性会自动重新计算
  },
  { immediate: false }
);
</script>

<style scoped lang="scss">
.water-quality-monitor {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.section-title {
  text-align: center;
  margin-bottom: 15px;
  font-size: 16px;
  color: #00d4ff;
  font-weight: bold;
}

.charts-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10px;
  overflow: hidden;
}

.chart-item {
  flex: 1;
  min-height: 0;

  .chart-title {
    text-align: center;
    font-size: 12px;
    color: #ccc;
    margin-bottom: 5px;
  }

  .chart {
    width: 100%;
    height: calc(100% - 20px);
  }
}
</style>
