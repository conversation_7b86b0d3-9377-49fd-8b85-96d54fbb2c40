<template>
  <div class="weather-widget">
    <div v-if="loading" class="loading">加载中...</div>
    <div v-else-if="weatherData" class="weather-content">
      <!-- 当前天气 -->
      <div class="current-weather">
        <div class="weather-main">
          <div class="weather-icon">
            <Icon :icon="getWeatherIcon(weatherData.current.weather)" />
          </div>
          <div class="weather-info">
            <div class="temperature">
              {{ weatherData.current.temperature }}°C
            </div>
            <div class="weather-desc">{{ weatherData.current.weather }}</div>
            <div class="location">{{ weatherData.location }}</div>
          </div>
        </div>

        <div class="weather-details">
          <div class="detail-item">
            <span class="label">风向:</span>
            <span class="value">{{ weatherData.current.windDirection }}</span>
          </div>
          <div class="detail-item">
            <span class="label">风速:</span>
            <span class="value">{{ weatherData.current.windSpeed }}km/h</span>
          </div>
          <div class="detail-item">
            <span class="label">气压:</span>
            <span class="value">{{ weatherData.current.pressure }}hPa</span>
          </div>
          <div class="detail-item">
            <span class="label">湿度:</span>
            <span class="value">{{ weatherData.current.humidity }}%</span>
          </div>
        </div>
      </div>

      <!-- 未来3天天气 -->
      <div class="forecast">
        <div class="forecast-title">未来3天天气</div>
        <div class="forecast-list">
          <div
            v-for="(day, index) in weatherData.forecast"
            :key="index"
            class="forecast-item"
          >
            <div class="forecast-date">{{ day.date }}</div>
            <div class="forecast-icon">
              <Icon :icon="getWeatherIcon(day.weather)" />
            </div>
            <div class="forecast-weather">{{ day.weather }}</div>
            <div class="forecast-temp">
              <span class="high">{{ day.high }}°</span>
              <span class="low">{{ day.low }}°</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-else class="error">天气数据加载失败</div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import dayjs from "dayjs";
import { Icon } from "@iconify/vue";

interface WeatherData {
  location: string;
  current: {
    temperature: number;
    weather: string;
    windDirection: string;
    windSpeed: number;
    pressure: number;
    humidity: number;
  };
  forecast: Array<{
    date: string;
    weather: string;
    high: number;
    low: number;
  }>;
}

const weatherData = ref<WeatherData | null>(null);
const loading = ref(true);

// 获取天气图标
const getWeatherIcon = (weather: string) => {
  const iconMap: Record<string, string> = {
    晴: "mdi:weather-sunny",
    多云: "mdi:weather-partly-cloudy",
    阴: "mdi:weather-cloudy",
    小雨: "mdi:weather-rainy",
    中雨: "mdi:weather-pouring",
    大雨: "mdi:weather-lightning-rainy",
    雷阵雨: "mdi:weather-lightning",
    雪: "mdi:weather-snowy",
    雾: "mdi:weather-fog",
  };

  for (const key in iconMap) {
    if (weather.includes(key)) {
      return iconMap[key];
    }
  }

  return "mdi:weather-partly-cloudy";
};

// 获取真实天气数据
const fetchWeatherData = async () => {
  try {
    loading.value = true;

    // 这里使用模拟数据，实际项目中可以接入真实的天气API
    // 比如和风天气、OpenWeatherMap等
    await new Promise((resolve) => setTimeout(resolve, 1000)); // 模拟API请求延迟

    const mockWeatherData: WeatherData = {
      location: "广西北海",
      current: {
        temperature: 28,
        weather: "多云",
        windDirection: "东南风",
        windSpeed: 12,
        pressure: 1013,
        humidity: 75,
      },
      forecast: [
        {
          date: dayjs().add(1, "day").format("MM-DD"),
          weather: "晴",
          high: 30,
          low: 22,
        },
        {
          date: dayjs().add(2, "day").format("MM-DD"),
          weather: "小雨",
          high: 26,
          low: 20,
        },
        {
          date: dayjs().add(3, "day").format("MM-DD"),
          weather: "多云",
          high: 29,
          low: 23,
        },
      ],
    };

    weatherData.value = mockWeatherData;
  } catch (error) {
    console.error("获取天气数据失败:", error);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  fetchWeatherData();

  // 每小时更新一次天气数据
  setInterval(fetchWeatherData, 60 * 60 * 1000);
});
</script>

<style scoped lang="scss">
.weather-widget {
  height: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.loading,
.error {
  text-align: center;
  color: #0efcff;
  padding: 20px;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.weather-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
  flex: 1;
}

.current-weather {
  .weather-main {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;

    .weather-icon {
      font-size: 48px;
      color: #0efcff;
    }

    .weather-info {
      flex: 1;

      .temperature {
        font-size: 32px;
        font-weight: bold;
        color: #fff;
        line-height: 1;
      }

      .weather-desc {
        font-size: 16px;
        color: #ccc;
        margin: 5px 0;
      }

      .location {
        font-size: 14px;
        color: #0efcff;
      }
    }
  }

  .weather-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;

    .detail-item {
      display: flex;
      justify-content: space-between;
      font-size: 14px;

      .label {
        color: #ccc;
      }

      .value {
        color: #fff;
      }
    }
  }
}

.forecast {
  .forecast-title {
    font-size: 14px;
    color: #00d4ff;
    margin-bottom: 10px;
    text-align: center;
  }

  .forecast-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .forecast-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 4px;

    .forecast-date {
      width: 40px;
      font-size: 12px;
      color: #ccc;
    }

    .forecast-icon {
      width: 24px;
      font-size: 20px;
      color: #00d4ff;
      text-align: center;
    }

    .forecast-weather {
      flex: 1;
      font-size: 12px;
      color: #fff;
    }

    .forecast-temp {
      font-size: 12px;

      .high {
        color: #ff6b6b;
        margin-right: 5px;
      }

      .low {
        color: #74c0fc;
      }
    }
  }
}
</style>
